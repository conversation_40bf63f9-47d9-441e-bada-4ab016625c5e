# STAGING ENVIRONMENT TEMPLATE
# Copy this to .env.staging and fill in actual values

# Staging Database (Safe to reset/modify)
NEXT_PUBLIC_SUPABASE_URL=https://btwaueeckvylrlrnbvgt.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_staging_anon_key_here
SUPABASE_URL=https://btwaueeckvylrlrnbvgt.supabase.co
SUPABASE_SERVICE_KEY=your_staging_service_key_here

# Staging Stripe (Test Mode - Safe)
STRIPE_SECRET_KEY=sk_test_your_test_key_here
STRIPE_WEBHOOK_SECRET=whsec_your_test_webhook_here
STRIPE_PUBLISHABLE_KEY=pk_test_your_test_publishable_here
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_your_test_publishable_here

# Environment Identification
ENVIRONMENT=staging
TARGET_ENVIRONMENT=staging

# External Services (use staging/test versions)
NEXT_PUBLIC_TURNSTILE_SITE_KEY=your_turnstile_key
REDIS_URL=your_staging_redis_url
VOYAGE_API_KEY=your_voyage_api_key
GROQ_API_KEY=your_groq_api_key
RESEND_API_KEY=your_resend_api_key
NEXT_PUBLIC_SENTRY_DSN=your_staging_sentry_dsn

# LangExtract Integration - Staging Configuration
LANGEXTRACT_SERVICE_URL=https://ailex-langextract-staging-xxx.run.app
LANGEXTRACT_AUTH_TOKEN=staging-token-placeholder
GOOGLE_CLOUD_PROJECT=pi-lawyer-ai-staging
GOOGLE_CLOUD_LOCATION=us-central1
VERTEX_AI_ENABLE_ZDR=true
VERTEX_AI_LOCATION=us-central1

# CopilotKit Configuration
COPILOTKIT_API_KEY=your_copilotkit_api_key_here
NEXT_PUBLIC_COPILOT_KEY=your_copilotkit_api_key_here

# Medical Records Feature Flags
FEATURE_MEDICAL_RECORDS=true
NEXT_PUBLIC_FEATURE_MEDICAL_RECORDS=true
FEATURE_MEDICAL_CHRONOLOGY=true
FEATURE_MEDICAL_GAP_DETECTION=true

# Medical Records Processing Configuration
MEDICAL_QUEUE_NAME=medical-records-processing-staging
MEDICAL_QUEUE_LOCATION=us-central1
MEDICAL_QUEUE_MAX_RETRIES=3
MEDICAL_QUEUE_TIMEOUT=600

# HIPAA Compliance Configuration
HIPAA_AUDIT_ENABLED=true
HIPAA_ENCRYPTION_KEY=projects/pi-lawyer-ai-staging/locations/us-central1/keyRings/medical-records/cryptoKeys/phi-key
HIPAA_VPC_SC_PERIMETER=medical-records-perimeter-staging

# Add all other environment variables from main .env...
