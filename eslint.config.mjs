/* AILEX ESLint BASELINE v1 — DO NOT MODIFY WITHOUT OWNER APPROVAL */
import tseslint from 'typescript-eslint';
import vitest from 'eslint-plugin-vitest';
import reactHooks from 'eslint-plugin-react-hooks';
import unusedImports from 'eslint-plugin-unused-imports';
import globals from 'globals';

export default [
  // --- Ignores (flat config replaces .eslintignore for these) ---
  {
    name: 'ailex/ignores',
    ignores: [
      '**/node_modules/**',
      '**/.next/**',
      '**/dist/**',
      '**/build/**',
      '**/coverage/**',
      '**/.turbo/**',
      '**/.vercel/**',
      '**/.cache/**',
      '**/generated/**',
      '**/out/**',
      '**/next-env.d.ts', // Next.js generated file with triple-slash references
      '**/venv/**', // Python virtual environment
      '**/.trees/**', // Tree files and temporary directories
      '**/bulk-function-migration.js', // Utility scripts
      '**/check-tasks-schema.js',
      '**/check_env_vars.js',
      '**/verify-mcp-integration.ts',
      '**/verify-migration-progress.js',
      '**/.trees/damages-calculator/**', // Specific tree directory
      // Additional utility and migration scripts
      '**/api-based-migration.js',
      '**/complete-migration-batch.js',
      '**/critical-tables-migration.js',
      '**/client-intake-test.js',
      '**/check_jwt.js',
      '**/test-staging-environment.js',
      '**/test_mcp.js',
      '**/test_supabase_templates.js',
      '**/test_template_rls.js',
      '**/test_templates.js',
      // Backend directories that appear to be temporary/experimental
      '**/apps/backend/**',
      '**/backend/**',
      // Cloud functions build output
      '**/cloud-functions/lib/**',
      // Coverage and test output
      '**/htmlcov/**',
      // Cypress tests (should use separate config)
      '**/cypress/**',
      // E2E tests (should use separate config)
      '**/tests/**/*.e2e.spec.ts',
    ],
  },

  // --- Base TypeScript rules (non type-aware; fast & CI-stable) ---
  ...tseslint.configs.recommended,

  // --- Repository-wide defaults ---
  {
    name: 'ailex/base',
    plugins: {
      vitest,
      'react-hooks': reactHooks,
      'unused-imports': unusedImports,
    },
    languageOptions: {
      ecmaVersion: 'latest',
      sourceType: 'module',
      globals: {
        ...globals.es2023,
      },
    },
    rules: {
      // correctness / foot-gun guards
      'no-undef': 'error',
      'no-unreachable': 'error',
      'eqeqeq': ['error', 'smart'],
      // Note: @typescript-eslint/no-floating-promises requires type-aware linting, removed for CI stability

      // logging policy
      'no-console': ['warn', { allow: ['warn', 'error'] }],

      // unused import/var handling (fast, reliable)
      '@typescript-eslint/no-unused-vars': 'off',
      'unused-imports/no-unused-imports': 'error',
      'unused-imports/no-unused-vars': ['warn', {
        argsIgnorePattern: '^_',
        varsIgnorePattern: '^_',
        ignoreRestSiblings: true,
      }],

      // React Hooks best practices
      'react-hooks/rules-of-hooks': 'error',
      'react-hooks/exhaustive-deps': 'warn',
    },
  },

  // --- Browser code (app/client) ---
  {
    name: 'ailex/browser',
    files: ['frontend/src/**/*.{ts,tsx}', 'apps/**/src/**/*.{ts,tsx}'],
    languageOptions: {
      globals: {
        ...globals.browser,
        ...globals.es2023,
      },
    },
  },

  // --- Node code (API routes, server files, scripts, configs) ---
  {
    name: 'ailex/node',
    files: [
      'frontend/src/pages/api/**/*.ts',
      'frontend/src/app/**/route.ts',
      'frontend/src/**/server/**/*.{ts,tsx}',
      'scripts/**/*.{ts,js}',
      '*.config.*',
      'vitest.*.config.*',
      'eslint.config.*',
    ],
    languageOptions: {
      globals: {
        ...globals.node,
        ...globals.es2023,
      },
    },
  },

  // --- Tests (Vitest) ---
  {
    name: 'ailex/tests',
    files: ['**/*.{test,spec}.{ts,tsx,js,jsx}', 'vitest.setup.ts', 'frontend/vitest.setup.ts'],
    languageOptions: {
      globals: {
        ...globals.node,
        ...vitest.environments.env.globals,
      },
    },
    rules: {
      'no-console': 'off',
      'vitest/no-focused-tests': 'error',
      'vitest/no-identical-title': 'error',
      'vitest/no-disabled-tests': 'warn',
    },
  },
];
