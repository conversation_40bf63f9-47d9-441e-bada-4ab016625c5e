/* AILEX ESLint BASELINE v1 — DO NOT MODIFY WITHOUT OWNER APPROVAL */
import tseslint from 'typescript-eslint';
import vitest from 'eslint-plugin-vitest';
import reactHooks from 'eslint-plugin-react-hooks';
import unusedImports from 'eslint-plugin-unused-imports';
import globals from 'globals';

export default [
  // --- Ignores (flat config replaces .eslintignore for these) ---
  {
    name: 'ailex/ignores',
    ignores: [
      '**/node_modules/**',
      '**/.next/**',
      '**/dist/**',
      '**/build/**',
      '**/coverage/**',
      '**/.turbo/**',
      '**/.vercel/**',
      '**/.cache/**',
      '**/generated/**',
      '**/out/**',
      '**/next-env.d.ts', // Next.js generated file with triple-slash references
      '**/venv/**', // Python virtual environment
      '**/.trees/**', // Tree files and temporary directories
      // All utility and migration scripts in root
      '*.js', // All root-level JS files (utility scripts)
      '*.ts', // All root-level TS files (utility scripts)
      '!eslint.config.mjs', // Keep ESLint config
      '!vitest.*.config.ts', // Keep Vitest configs
      // Backend directories that appear to be temporary/experimental
      '**/apps/backend/**',
      '**/backend/**',
      // Cloud functions build output
      '**/cloud-functions/**',
      // Coverage and test output
      '**/htmlcov/**',
      // E2E test directories (should use separate configs)
      '**/cypress/**',
      '**/e2e/**',
      '**/tests/**',
      // Legacy test files
      '**/frontend/__legacy__/**',
      // Scripts directory
      '**/scripts/**',
      // Supabase functions (Deno environment)
      '**/supabase/functions/**',
      // Python files and directories
      '**/*.py',
      '**/__pycache__/**',
      '**/.pytest_cache/**',
      // Python virtual environments
      '**/langgraph-migration-env/**',
      '**/venv/**',
      '**/env/**',
      // Package directories
      '**/packages/**',
      // Performance testing
      '**/perf/**',
      // Mock files
      '**/__mocks__/**',
      // Config files that should use Node environment
      '**/jest.config.js',
      '**/jest.setup.js',
      '**/cypress.config.ts',
      '**/drizzle.config.ts',
      '**/vitest.*.config.ts',
      '**/debug-validator.js',
      '**/fix-eslint-issues.js',
      '**/analyze-tests.mjs',
      '**/audit-script.ts',
      // Load test files (k6 environment)
      '**/load-tests/**',
      // Additional utility files
      '**/test-matters-api.js',
    ],
  },

  // --- Base TypeScript rules (non type-aware; fast & CI-stable) ---
  ...tseslint.configs.recommended,

  // --- Repository-wide defaults ---
  {
    name: 'ailex/base',
    plugins: {
      vitest,
      'react-hooks': reactHooks,
      'unused-imports': unusedImports,
    },
    languageOptions: {
      ecmaVersion: 'latest',
      sourceType: 'module',
      globals: {
        ...globals.es2023,
      },
    },
    rules: {
      // correctness / foot-gun guards
      'no-undef': 'error',
      'no-unreachable': 'error',
      'eqeqeq': ['error', 'smart'],
      // Note: @typescript-eslint/no-floating-promises requires type-aware linting, removed for CI stability

      // logging policy
      'no-console': ['warn', { allow: ['warn', 'error'] }],

      // unused import/var handling (fast, reliable)
      '@typescript-eslint/no-unused-vars': 'off',
      'unused-imports/no-unused-imports': 'error',
      'unused-imports/no-unused-vars': ['warn', {
        argsIgnorePattern: '^_',
        varsIgnorePattern: '^_',
        ignoreRestSiblings: true,
      }],

      // React Hooks best practices
      'react-hooks/rules-of-hooks': 'error',
      'react-hooks/exhaustive-deps': 'warn',
    },
  },

  // --- Browser code (app/client) ---
  {
    name: 'ailex/browser',
    files: ['frontend/src/**/*.{ts,tsx}', 'apps/**/src/**/*.{ts,tsx}', 'src/**/*.{ts,tsx}'],
    languageOptions: {
      globals: {
        ...globals.browser,
        ...globals.es2023,
        React: 'readonly',
        JSX: 'readonly',
        document: 'readonly',
        window: 'readonly',
        HTMLDivElement: 'readonly',
        HTMLButtonElement: 'readonly',
        DragEvent: 'readonly',
        RequestInfo: 'readonly',
        ResponseInit: 'readonly',
      },
    },
  },

  // --- Node code (API routes, server files, scripts, configs) ---
  {
    name: 'ailex/node',
    files: [
      'frontend/src/pages/api/**/*.ts',
      'frontend/src/app/**/route.ts',
      'frontend/src/**/server/**/*.{ts,tsx}',
      'src/app/api/**/*.ts',
      'src/lib/**/*.ts',
      'scripts/**/*.{ts,js}',
      '*.config.*',
      'vitest.*.config.*',
      'eslint.config.*',
    ],
    languageOptions: {
      globals: {
        ...globals.node,
        ...globals.es2023,
        console: 'readonly',
        process: 'readonly',
        Buffer: 'readonly',
        URL: 'readonly',
        crypto: 'readonly',
        fetch: 'readonly',
        Request: 'readonly',
        Response: 'readonly',
        FormData: 'readonly',
      },
    },
  },

  // --- Tests (Vitest) ---
  {
    name: 'ailex/tests',
    files: ['**/*.{test,spec}.{ts,tsx,js,jsx}', 'vitest.setup.ts', 'frontend/vitest.setup.ts', '**/test/**/*.ts', '**/test/**/*.tsx'],
    languageOptions: {
      globals: {
        ...globals.node,
        ...globals.browser,
        ...vitest.environments.env.globals,
        jest: 'readonly',
        expect: 'readonly',
        describe: 'readonly',
        it: 'readonly',
        test: 'readonly',
        beforeEach: 'readonly',
        afterEach: 'readonly',
        beforeAll: 'readonly',
        afterAll: 'readonly',
        fail: 'readonly',
        global: 'readonly',
        supabase: 'readonly',
        window: 'readonly',
        document: 'readonly',
        RequestInit: 'readonly',
        MessageEvent: 'readonly',
        URLSearchParams: 'readonly',
        Buffer: 'readonly',
        process: 'readonly',
        __dirname: 'readonly',
        Mock: 'readonly',
        CyHttpMessages: 'readonly',
        InsurancePolicyRow: 'readonly',
      },
    },
    rules: {
      'no-console': 'off',
      'vitest/no-focused-tests': 'error',
      'vitest/no-identical-title': 'error',
      'vitest/no-disabled-tests': 'warn',
      '@typescript-eslint/no-explicit-any': 'warn', // Allow any in tests
    },
  },
];
