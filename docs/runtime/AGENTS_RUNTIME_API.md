# Agents Runtime API

This document describes the HTTP endpoints exposed by the FastAPI runtime for invoking LangGraph workflows and agents.

## Overview

The runtime lazily builds and caches graphs via a lightweight registry to avoid startup failures when optional environment variables are missing. If a graph cannot be created, the `/health` endpoint reports diagnostics and invocation endpoints return a `503` with details.

## Endpoints

### GET `/health`

- Purpose: Service health and graph readiness
- Response fields:
  - `status`: `ok` if the service is running
  - `environment`: current environment name
  - `uptime_seconds`: integer uptime
  - `graphs.master`: `{ ready: boolean, error?: string }`
  - `graphs.agents`: map of agent name to `{ ready: boolean, error?: string }`

Notes: The runtime attempts to initialize the master graph lazily on first health check; failures are non-fatal and surfaced in the response.

### POST `/agents/master/invoke`

- Purpose: Invoke the master routing graph
- Request body:

```json
{
  "state": { "messages": [ { "type": "human", "content": "Schedule a meeting tomorrow" } ], "tenant_id": "t1", "user_id": "u1" },
  "config": { "configurable": { } }
}
```

- Response: Graph result state (JSON)
- Errors:
  - `503` if the master graph is unavailable (with `status` details)
  - `500` for unexpected invocation failures

### POST `/agents/{agent_name}/invoke`

- Purpose: Invoke a specific agent or compiled graph
- Supported names (initial):
  - `calendar_graph`, `task_graph`
  - `matter_client_agent`, `research_agent`, `intake_agent`
  - `document_agent`, `deadline_agent`, `supervisor_agent`
- Request body:

```json
{ "state": { }, "config": { } }
```

- Response: Agent/graph result state (JSON)
- Errors:
  - `503` if the agent is unavailable or failed to initialize
  - `500` for unexpected invocation failures

## Environment Considerations

- The master router can use a Voyage client for LLM-assisted intent detection. If `VOYAGE_API_KEY` is not provided, the router falls back to keyword-based detection automatically.
- Some agents may rely on additional environment variables (e.g., deadline/document agents); if unavailable, they will surface their status as unavailable in `/health` and return `503` on invocation.

## Versioning

- Runtime API Version: 1.0.0

