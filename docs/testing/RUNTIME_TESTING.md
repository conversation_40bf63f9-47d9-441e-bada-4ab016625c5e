# Runtime & Agent Testing Guide

This guide explains how to validate the LangGraph runtime endpoints and run targeted integration tests locally.

## Prerequisites

Set these environment variables (staging values OK for local):

```
MCP_RULES_BASE=https://rules.ailexlaw.com
STRIPE_SECRET_KEY=sk_test_...
STRIPE_WEBHOOK_SECRET=whsec_...
OPENAI_API_KEY=sk-...
VOYAGE_API_KEY=voyage-...
```

The runtime tolerates missing Voyage client by falling back to keyword routing, but LLM+keyword consensus is better with `VOYAGE_API_KEY` configured.

## Endpoints

- Health: `GET /health` – reports master/agents readiness and diagnostics
- Master invoke: `POST /agents/master/invoke`
- Agent invoke: `POST /agents/{agent_name}/invoke` (e.g., `research_agent`, `calendar_graph`)
- CopilotKit: `POST /copilotkit` – Cloud Remote Endpoint integration

## Quick Smokes

- Master (research):
```
curl -X POST http://localhost:8000/agents/master/invoke \
  -H 'Content-Type: application/json' \
  -d '{
    "state": {"messages": [{"type": "human", "content": "Research negligence law in Texas"}]},
    "config": {"configurable": {"tenant_id": "t1", "user_id": "u1", "thread_id": "th1"}}
  }'
```

- Calendar:
```
curl -X POST http://localhost:8000/agents/master/invoke \
  -H 'Content-Type: application/json' \
  -d '{"state": {"messages": [{"type": "human", "content": "Schedule a meeting tomorrow at 3pm"}]}}'
```

- Research (direct):
```
curl -X POST http://localhost:8000/agents/research_agent/invoke \
  -H 'Content-Type: application/json' \
  -d '{
    "state": {"messages": [{"type": "human", "content": "statute of limitations for personal injury in Texas"}]},
    "config": {"configurable": {"tenant_id": "t1", "user_id": "u1", "thread_id": "th1"}}
  }'
```

## Running Integration Tests

Targeted test commands (from repo root):

- Research agentic wrapper (patched for local):
```
export MCP_RULES_BASE=... STRIPE_SECRET_KEY=... STRIPE_WEBHOOK_SECRET=... OPENAI_API_KEY=... VOYAGE_API_KEY=...
pytest backend/agents/interactive/research/tests/test_agentic_wrapper.py -q
```

- Master router suites:
```
export MCP_RULES_BASE=... STRIPE_SECRET_KEY=... STRIPE_WEBHOOK_SECRET=... OPENAI_API_KEY=... VOYAGE_API_KEY=...
pytest backend/agents/tests/integration/test_master_router_validation.py -q
pytest backend/agents/tests/integration/test_enhanced_master_router_validation.py -q
```

If your environment restricts network access, some suites may skip or require additional mocking (Redis, Celery, external LLMs). The research test fixtures are patched to avoid network.

## Notes

- Message serialization: `/agents/*` endpoints normalize message objects; raw LangChain messages are converted to `{role, content}` to ensure JSON output.
- Voyage client: The runtime adapts the Voyage client to provide a `generate_text(prompt)` method. If it cannot import your actual client, it falls back to keyword routing.
- Agents requiring config: Research and Intake expect `tenant_id`, `user_id`, `thread_id` under `config.configurable`.

