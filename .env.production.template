# PRODUCTION ENVIRONMENT TEMPLATE
# Copy this to .env.production and fill in actual values
# ⚠️ NEVER COMMIT ACTUAL PRODUCTION SECRETS TO GIT

# Production Database (LIVE CUSTOMER DATA)
NEXT_PUBLIC_SUPABASE_URL=https://anwefmklplkjxkmzpnva.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_production_anon_key_here
SUPABASE_URL=https://anwefmklplkjxkmzpnva.supabase.co
SUPABASE_SERVICE_KEY=your_production_service_key_here

# Production Stripe (LIVE MODE - REAL MONEY)
STRIPE_SECRET_KEY=sk_live_your_live_key_here
STRIPE_WEBHOOK_SECRET=whsec_your_live_webhook_here
STRIPE_PUBLISHABLE_KEY=pk_live_your_live_publishable_here
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_live_your_live_publishable_here

# Environment Identification
ENVIRONMENT=production
TARGET_ENVIRONMENT=production

# CopilotKit Configuration
COPILOTKIT_API_KEY=your_production_copilotkit_api_key
NEXT_PUBLIC_COPILOT_KEY=your_production_copilotkit_api_key

# External Services (use production versions)
NEXT_PUBLIC_TURNSTILE_SITE_KEY=your_production_turnstile_key
REDIS_URL=your_production_redis_url
VOYAGE_API_KEY=your_production_voyage_key
GROQ_API_KEY=your_production_groq_key
RESEND_API_KEY=your_production_resend_key
NEXT_PUBLIC_SENTRY_DSN=your_production_sentry_dsn

# Add all other environment variables from main .env...
