#!/usr/bin/env node

// Load environment variables from .env.local
require('dotenv').config({ path: './frontend/.env.local' });

console.log('🔍 Testing CopilotKit Environment Variables');
console.log('');
console.log('Environment Variables:');
console.log('COPILOTKIT_API_KEY:', process.env.COPILOTKIT_API_KEY ? '✅ SET' : '❌ NOT SET');
console.log('NEXT_PUBLIC_COPILOT_KEY:', process.env.NEXT_PUBLIC_COPILOT_KEY ? '✅ SET' : '❌ NOT SET');

if (process.env.COPILOTKIT_API_KEY) {
  console.log('Server-side API Key (first 20 chars):', process.env.COPILOTKIT_API_KEY.substring(0, 20) + '...');
}
if (process.env.NEXT_PUBLIC_COPILOT_KEY) {
  console.log('Client-side API Key (first 20 chars):', process.env.NEXT_PUBLIC_COPILOT_KEY.substring(0, 20) + '...');
}

console.log('');
console.log('🧪 Testing API Key Format:');
const apiKey = process.env.NEXT_PUBLIC_COPILOT_KEY;
if (apiKey) {
  console.log('Key starts with "ck_pub_":', apiKey.startsWith('ck_pub_') ? '✅ YES' : '❌ NO');
  console.log('Key length:', apiKey.length, 'characters');
}
