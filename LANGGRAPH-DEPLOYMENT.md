# LangGraph FastAPI Deployment Guide

This document provides instructions for building and deploying the LangGraph FastAPI application using Docker and Fly.io.

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Local Development](#local-development)
3. [Building the Docker Image](#building-the-docker-image)
4. [Testing Locally](#testing-locally)
5. [Deploying to Fly.io](#deploying-to-flyio)
6. [Environment Variables](#environment-variables)
7. [Monitoring and Logs](#monitoring-and-logs)
8. [Troubleshooting](#troubleshooting)

## Prerequisites

- Docker installed on your local machine
- Fly.io CLI installed (`flyctl`)
- Fly.io account and authentication set up
- Required API keys and credentials for:
  - OpenAI
  - Pinecone
  - Supabase
  - CopilotKit

## Local Development

1. Create a virtual environment:

   ```bash
   python -m venv langgraph-migration-env
   source langgraph-migration-env/bin/activate  # On Windows: langgraph-migration-env\Scripts\activate
   ```

2. Install dependencies:

   ```bash
   pip install -r requirements.txt
   ```

3. Create a `.env` file with required environment variables (see [Environment Variables](#environment-variables) section).

4. Run the FastAPI server:
   ```bash
   python run_fastapi.py
   # or
   uvicorn src.pi_lawyer.api.runtime:app --host 0.0.0.0 --port 8000 --reload
   ```

## Building the Docker Image

Build the Docker image locally:

```bash
docker build -t pi-lawyer-langgraph .
```

## Testing Locally

Run the Docker container locally:

```bash
docker run -p 8000:8000 --env-file .env pi-lawyer-langgraph
```

Test the API:

```bash
curl http://localhost:8000/health
```

## Deploying to Fly.io

1. Launch the app on Fly.io (first time only):

   ```bash
   fly launch --dockerfile Dockerfile --name pi-lawyer-langgraph
   ```

2. Set required secrets:

   ```bash
   fly secrets set OPENAI_API_KEY=your_openai_api_key \
                  PINECONE_API_KEY=your_pinecone_api_key \
                  PINECONE_ENVIRONMENT=your_pinecone_environment \
                  PINECONE_INDEX_NAME=your_pinecone_index_name \
                  SUPABASE_URL=your_supabase_url \
                  SUPABASE_KEY=your_supabase_key \
                  CPK_ENDPOINT_SECRET=your_endpoint_secret \
                  DB_PASSWORD=your_db_password \
                  DB_USER=postgres \
                  DB_NAME=postgres \
                  DB_HOST=your_db_host \
                  DB_PORT=5432
   ```

3. Deploy the application:

   ```bash
   fly deploy
   ```

4. Open the deployed application:
   ```bash
   fly open
   ```

## Environment Variables

The following environment variables are required:

| Variable               | Description                                                | Required |
| ---------------------- | ---------------------------------------------------------- | -------- |
| `OPENAI_API_KEY`       | OpenAI API key                                             | Yes      |
| `PINECONE_API_KEY`     | Pinecone API key                                           | Yes      |
| `PINECONE_ENVIRONMENT` | Pinecone environment (e.g., us-east-1)                     | Yes      |
| `PINECONE_INDEX_NAME`  | Pinecone index name                                        | Yes      |
| `SUPABASE_URL`         | Supabase project URL                                       | Yes      |
| `SUPABASE_KEY`         | Supabase API key                                           | Yes      |
| `CPK_ENDPOINT_SECRET`  | CopilotKit endpoint secret                                 | Yes      |
| `DB_PASSWORD`          | Database password                                          | Yes      |
| `DB_USER`              | Database user                                              | Yes      |
| `DB_NAME`              | Database name                                              | Yes      |
| `DB_HOST`              | Database host                                              | Yes      |
| `DB_PORT`              | Database port                                              | Yes      |
| `LANGSMITH_API_KEY`    | LangSmith API key for tracing                              | No       |
| `LANGSMITH_PROJECT`    | LangSmith project name                                     | No       |
| `PORT`                 | Port to run the server on (default: 8000)                  | No       |
| `APP_ENV`              | Application environment (development, staging, production) | No       |
| `LOG_LEVEL`            | Logging level (DEBUG, INFO, WARNING, ERROR)                | No       |
| `CORS_ORIGINS`         | Comma-separated list of allowed origins for CORS           | No       |

## Monitoring and Logs

View application logs:

```bash
fly logs
```

Monitor the application:

```bash
fly status
```

Access the health check endpoint:

```bash
curl https://pi-lawyer-langgraph.fly.dev/health
```

## Runtime API Reference

The FastAPI runtime exposes HTTP endpoints to invoke the master router and individual agents/graphs.

- See `docs/runtime/AGENTS_RUNTIME_API.md` for details on:
  - `/health` graph readiness and diagnostics
  - `POST /agents/master/invoke` to invoke the master routing graph
  - `POST /agents/{agent_name}/invoke` to invoke a specific agent/graph

### Quickstart

Run the server locally and try these examples.

1) Invoke the master router

```
curl -X POST http://localhost:8000/agents/master/invoke \
  -H 'Content-Type: application/json' \
  -d '{
    "state": {
      "messages": [{"type": "human", "content": "Schedule a meeting tomorrow at 3pm"}],
      "tenant_id": "tenant-123",
      "user_id": "user-456"
    }
  }'
```

2) Invoke a compiled graph directly (calendar)

```
curl -X POST http://localhost:8000/agents/calendar_graph/invoke \
  -H 'Content-Type: application/json' \
  -d '{
    "state": {
      "messages": [{"type": "human", "content": "Create an event next Tuesday at 10am"}]
    }
  }'
```

3) Invoke a BaseAgent-based agent (research)

Note: BaseAgent agents require `tenant_id`, `user_id`, and `thread_id` in `config.configurable`.

```
curl -X POST http://localhost:8000/agents/research_agent/invoke \
  -H 'Content-Type: application/json' \
  -d '{
    "state": {
      "messages": [{"role": "user", "content": "Research statute of limitations for personal injury in Texas"}]
    },
    "config": {
      "configurable": {"tenant_id": "tenant-123", "user_id": "user-456", "thread_id": "thread-abc"}
    }
  }'
```



## Troubleshooting

### Common Issues

1. **Missing Environment Variables**:
   - Check that all required environment variables are set in Fly.io secrets.
   - Verify the values are correct.

2. **Container Fails to Start**:
   - Check the logs with `fly logs`.
   - Ensure the database connection is working.
   - Verify API keys are valid.

3. **API Errors**:
   - Check the health endpoint for component status.
   - Verify that all dependencies are properly installed.
   - Check for rate limiting or authentication issues with external services.

### Getting Help

If you encounter issues not covered here, please:

1. Check the FastAPI and LangGraph documentation.
2. Review the Fly.io deployment guides.
3. Contact the development team for assistance.
