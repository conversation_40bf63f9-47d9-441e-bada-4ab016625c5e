"use client";

import { CopilotKit } from "@copilotkit/react-core";
import { getCopilotKitConfig } from "@/lib/env/client";

export default function DocumentsLayout({
  children,
}: {
  children: React.ReactElement;
}) {
  const copilotConfig = getCopilotKitConfig();

  return (
    <CopilotKit
      publicApiKey={copilotConfig.apiKey}
      agent="document_agent"
      context={{
        userRole: "staff",
        context: "documents",
        threadId: crypto.randomUUID(), // In production, use org+user ID hash
      }}
    >
      {children}
    </CopilotKit>
  );
}
