"use client";

import { CopilotKit } from "@copilotkit/react-core";
import { getCopilotKitConfig } from "@/lib/env/client";

export default function IntakeLayout({
  children,
}: {
  children: React.ReactElement;
}) {
  const copilotConfig = getCopilotKitConfig();

  return (
    <CopilotKit
      publicApiKey={copilotConfig.apiKey}
      agent="intake_agent"
      properties={{
        userRole: "staff",
        context: "intake",
        threadId: crypto.randomUUID(), // In production, use org+user ID hash
      }}
    >
      {children}
    </CopilotKit>
  );
}
