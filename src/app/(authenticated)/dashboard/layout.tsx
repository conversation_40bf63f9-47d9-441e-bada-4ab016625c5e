"use client";

import { CopilotKit } from "@copilotkit/react-core";
import { getCopilotKitConfig } from "@/lib/env/client";

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const copilotConfig = getCopilotKitConfig();

  return (
    <CopilotKit
      publicApiKey={copilotConfig.apiKey}
      agent="supervisor_agent"
      context={{
        userRole: "staff",
        // Deterministic thread ID for tenant isolation
        threadId: crypto.randomUUID(), // In production, use org+user ID hash
      }}
    >
      {children}
    </CopilotKit>
  );
}
