"use client";

import { CopilotKit } from "@copilotkit/react-core";
import { useParams } from "next/navigation";
import { ClientPortalProvider } from "@/lib/client-portal/context";
import ClientErrorBoundary from "@/components/client-portal/error-boundary";
import { getCopilotKitConfig } from "@/lib/env/client";

export default function ClientPortalLayout({
  children,
}: {
  children: React.ReactElement;
}) {
  const params = useParams();
  const clientId =
    typeof params?.clientId === "string" ? params.clientId : undefined;
  const copilotConfig = getCopilotKitConfig();

  // Generate a deterministic thread ID for this client session
  // In production, use a hash of client ID + session ID
  const threadId = clientId || crypto.randomUUID();

  return (
    <ClientPortalProvider clientId={clientId}>
      <ClientErrorBoundary>
        <CopilotKit
          publicApiKey={copilotConfig.apiKey}
          agent="client_portal_agent"
          context={{
            userRole: "client",
            threadId: threadId,
          }}
        >
          {children}
        </CopilotKit>
      </ClientErrorBoundary>
    </ClientPortalProvider>
  );
}
