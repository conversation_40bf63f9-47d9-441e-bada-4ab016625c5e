"""
Graph Registry for LangGraph Workflows

This module centralizes creation and lazy-loading of the master routing graph
and selected agent graphs so that the FastAPI runtime can invoke them via
simple HTTP endpoints.

Design goals:
- Lazy initialization: avoid import-time failures when optional env vars are missing
- Fail-soft behavior: provide clear status/diagnostics in health without crashing
- Minimal coupling: do not assume all agents are available in every environment

Exposed API:
- GraphRegistry.get_master_graph()
- GraphRegistry.get_agent_invoker(name)
- GraphRegistry.status() – summarized readiness for health endpoints

Notes:
- The master graph is created via backend.agents.interactive.master_router.create_master_graph.
- We pass a VoyageClient if available; otherwise we pass None and the router
  will fall back to keyword-based detection (per implementation in master_router).
"""

from __future__ import annotations

import logging
from typing import Any, Dict, Optional

logger = logging.getLogger(__name__)


class _AgentInvoker:
    """Unified async invoker wrapper for agents or compiled graphs.

    If it's a compiled graph, it exposes `ainvoke`.
    If it's an agent instance, it exposes `__call__`.
    This wrapper normalizes both into a single `invoke(state, config)` coroutine.
    """

    def __init__(self, *, graph: Any | None = None, agent: Any | None = None) -> None:
        self._graph = graph
        self._agent = agent

    async def invoke(self, state: Dict[str, Any], config: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        if self._graph is not None:
            return await self._graph.ainvoke(state, config or {})
        if self._agent is not None:
            return await self._agent(state, config or {})
        raise RuntimeError("No graph or agent available to invoke")


class GraphRegistry:
    """Lazily builds and caches master and agent graphs for invocation.

    Usage:
        registry = GraphRegistry()
        master = registry.get_master_graph()
        invoker = registry.get_agent_invoker("calendar_graph")
        result = await invoker.invoke(state, config)
    """

    def __init__(self) -> None:
        self._master_graph = None
        self._status: Dict[str, Any] = {
            "master": {"ready": False, "error": None},
            "agents": {},
        }

    def _get_voyage_client(self) -> Optional[Any]:
        """Attempt to get a VoyageClient; return None if unavailable.

        The master router will gracefully fall back to keyword routing if the
        voyage client is not provided.
        """
        # Try multiple import paths to accommodate package layout
        for mod in (
            "shared.core.llm.voyage",
            "src.pi_lawyer.shared.core.llm.voyage",
            "pi_lawyer.shared.core.llm.voyage",
        ):
            try:
                module = __import__(mod, fromlist=["get_voyage_client"])
                client = getattr(module, "get_voyage_client")()
                # Ensure interface exposes async generate_text(prompt: str) -> str
                if not hasattr(client, "generate_text"):
                    client = self._wrap_voyage_client(client)
                return client
            except Exception as e:
                last_err = e
        logger.warning(
            "Voyage client not available; using keyword-only routing: %s",
            last_err if 'last_err' in locals() else "import error",
        )
        return None

    def _wrap_voyage_client(self, client: Any) -> Any:
        """Wrap a Voyage-like client to expose an async generate_text() method.

        This aligns with master_router's LLM detection. If OPENAI is configured, a
        future enhancement could call it here; for now we return a safe default JSON
        recommending supervisor_agent with low confidence to avoid failures.
        """

        class _Adapter:
            def __init__(self, inner: Any):
                self._inner = inner

            async def generate_text(self, prompt: str) -> str:  # pragma: no cover
                # Safe default; keyword detector will complement this
                return '{"agent": "supervisor_agent", "confidence": 0.2, "reasoning": "LLM unavailable"}'

            # Pass-through for other methods if needed
            def __getattr__(self, item):
                return getattr(self._inner, item)

        return _Adapter(client)

    def get_master_graph(self) -> Optional[Any]:
        """Return the compiled master graph, building it if needed.

        Returns None if the graph could not be built; check `status()` for errors.
        """
        if self._master_graph is not None:
            return self._master_graph

        try:
            from backend.agents.interactive.master_router import create_master_graph

            voyage = self._get_voyage_client()
            self._master_graph = create_master_graph(voyage=voyage)
            self._status["master"] = {"ready": True, "error": None}
        except Exception as e:
            logger.exception("Failed to create master graph: %s", e)
            # Try to build a partial master graph with available components
            try:
                self._master_graph = self._create_partial_master_graph()
                self._status["master"] = {"ready": True, "error": None, "partial": True}
            except Exception as ee:
                logger.exception("Failed to create partial master graph: %s", ee)
                self._master_graph = None
                self._status["master"] = {"ready": False, "error": str(e)}

        return self._master_graph

    def get_agent_invoker(self, name: str) -> Optional[_AgentInvoker]:
        """Return an invoker for an agent/graph name.

        Supported names (initial):
        - "calendar_graph"
        - "task_graph"
        - "matter_client_agent"
        - "research_agent"
        - "intake_agent"
        - "document_agent"
        - "deadline_agent"
        - "supervisor_agent"
        """
        key = name.strip()

        # If we already have a status record and it's marked as failed, do not retry here.
        agent_status = self._status["agents"].get(key)
        if agent_status and agent_status.get("ready") is False and agent_status.get("error"):
            return None

        try:
            # Build invokers lazily per requested name.
            if key == "calendar_graph":
                from backend.agents.interactive.calendar_crud.graph import create_calendar_graph

                graph = create_calendar_graph(voyage=self._get_voyage_client())
                invoker = _AgentInvoker(graph=graph)
            elif key == "task_graph":
                from backend.agents.interactive.task_crud.graph import create_task_graph

                graph = create_task_graph(voyage=self._get_voyage_client())
                invoker = _AgentInvoker(graph=graph)
            elif key == "matter_client_agent":
                from backend.agents.matter_client.agent import MatterClientAgent

                agent = MatterClientAgent()
                invoker = _AgentInvoker(agent=agent)
            elif key == "research_agent":
                from backend.agents.interactive.research.agent import ResearchAgent

                agent = ResearchAgent()
                invoker = _AgentInvoker(agent=agent)
            elif key == "intake_agent":
                from backend.agents.interactive.intake.agent import IntakeAgent

                agent = IntakeAgent()
                invoker = _AgentInvoker(agent=agent)
            elif key == "document_agent":
                from backend.agents.insights.document.agent import DocumentAgent

                agent = DocumentAgent()
                invoker = _AgentInvoker(agent=agent)
            elif key == "deadline_agent":
                from backend.agents.insights.deadline.agent import DeadlineInsightsAgent

                agent = DeadlineInsightsAgent()
                invoker = _AgentInvoker(agent=agent)
            elif key == "supervisor_agent":
                from backend.agents.insights.supervisor.agent import SupervisorAgent

                agent = SupervisorAgent()
                invoker = _AgentInvoker(agent=agent)
            else:
                self._status["agents"][key] = {"ready": False, "error": f"Unknown agent '{key}'"}
                return None

            self._status["agents"][key] = {"ready": True, "error": None}
            return invoker
        except Exception as e:
            logger.exception("Failed to create invoker for %s: %s", key, e)
            self._status["agents"][key] = {"ready": False, "error": str(e)}
            return None

    def status(self) -> Dict[str, Any]:
        """Return a snapshot of the registry status for health endpoints."""
        return {
            "master": self._status.get("master", {"ready": False, "error": "not_initialized"}),
            "agents": self._status.get("agents", {}),
        }

    def _create_partial_master_graph(self) -> Any:
        """Create a partial master graph based on available agents/graphs.

        Includes: master_router node, calendar_graph and task_graph. Attempts to add
        each specialized agent; if initialization fails, it is skipped. Routing defaults
        to available nodes.
        """
        from typing import Dict as _Dict
        from langgraph.graph import END, StateGraph
        from backend.agents.interactive.master_router import enhanced_parallel_master_router

        mapping: _Dict[str, str] = {}
        workflow = StateGraph(dict)

        voyage = self._get_voyage_client()

        async def master_router_with_voyage(state, config):  # pragma: no cover
            enhanced_config = config.copy() if config else {}
            if "configurable" not in enhanced_config:
                enhanced_config["configurable"] = {}
            if voyage is not None:
                enhanced_config["configurable"]["voyage_client"] = voyage
            return await enhanced_parallel_master_router(state, enhanced_config)

        workflow.add_node("master_router", master_router_with_voyage)

        # Always include calendar + task graphs
        try:
            from backend.agents.interactive.calendar_crud.graph import create_calendar_graph

            calendar_graph = create_calendar_graph(voyage=voyage) if voyage else create_calendar_graph(voyage=None)  # type: ignore
            workflow.add_node("calendar_graph", calendar_graph)
            workflow.add_edge("calendar_graph", END)
            mapping["calendar_graph"] = "calendar_graph"
        except Exception:
            pass

        try:
            from backend.agents.interactive.task_crud.graph import create_task_graph

            task_graph = create_task_graph(voyage=voyage) if voyage else create_task_graph(voyage=None)  # type: ignore
            workflow.add_node("task_graph", task_graph)
            workflow.add_edge("task_graph", END)
            mapping["task_graph"] = "task_graph"
        except Exception:
            pass

        # Attempt to include specialized agents, skipping failures
        def _try_agent(name: str, import_path: str):
            try:
                module_path, class_name = import_path.rsplit(".", 1)
                mod = __import__(module_path, fromlist=[class_name])
                cls = getattr(mod, class_name)
                agent = cls()
                async def _call(state, config, _agent=agent):
                    return await _agent(state, config)
                workflow.add_node(name, _call)
                workflow.add_edge(name, END)
                mapping[name] = name
                self._status["agents"][name] = {"ready": True, "error": None}
            except Exception as e:
                self._status["agents"][name] = {"ready": False, "error": str(e)}

        _try_agent("matter_client_agent", "backend.agents.matter_client.agent.MatterClientAgent")
        _try_agent("research_agent", "backend.agents.interactive.research.agent.ResearchAgent")
        _try_agent("intake_agent", "backend.agents.interactive.intake.agent.IntakeAgent")
        _try_agent("document_agent", "backend.agents.insights.document.agent.DocumentAgent")
        _try_agent("deadline_agent", "backend.agents.insights.deadline.agent.DeadlineInsightsAgent")
        _try_agent("supervisor_agent", "backend.agents.insights.supervisor.agent.SupervisorAgent")

        # Conditional routing — default to any available node
        def _route(state):  # pragma: no cover
            desired = state.get("next", "supervisor_agent")
            if desired in mapping:
                return desired
            # fallback order
            for k in ("calendar_graph", "task_graph", "supervisor_agent"):
                if k in mapping:
                    return k
            # last resort: pick any
            return next(iter(mapping.keys())) if mapping else "calendar_graph"

        workflow.add_conditional_edges("master_router", _route, mapping)
        workflow.set_entry_point("master_router")

        return workflow.compile()
