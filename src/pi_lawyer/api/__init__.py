"""
Minimal API package initializer (no side-effect imports).

Purpose:
- Avoid importing heavy routers at package import time to prevent import-time
  failures and enable lightweight consumers (e.g., TestClient runtime smoke).
- Application entrypoints (e.g., main.py or runtime.py) are responsible for
  mounting specific routers explicitly.

If you need an aggregated API router, prefer creating it in the entrypoint
to keep import costs minimal and prevent circular dependencies.
"""

from fastapi import APIRouter

# Export an empty router for compatibility; app entrypoints should include what they need.
api_router = APIRouter()

__all__ = ["api_router"]
