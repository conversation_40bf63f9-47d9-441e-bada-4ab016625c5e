"""
PI Lawyer AI – FastAPI Runtime with LangGraph Invocation Endpoints

This runtime exposes:
 - GET /health: service and graph readiness
 - GET /: simple root message
 - POST /agents/master/invoke: invoke the master routing graph (LangGraph)
 - POST /agents/{agent_name}/invoke: invoke a specific agent graph/agent

The runtime lazily builds graphs via a small GraphRegistry to avoid import-time
failures when optional integration env variables are absent. If a graph cannot
be built, the health endpoint will show diagnostics and invocation endpoints
return a 503 with a clear error message.
"""

import os
import sys
import logging
from typing import Any, Dict, Optional, List

# Configure logging first
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger("pi_lawyer.api")

logger.info("Starting minimal PI Lawyer AI runtime...")

# Import FastAPI with error handling
try:
    from fastapi import FastAPI, HTTPException, Path
    from fastapi.middleware.cors import CORSMiddleware
    from fastapi.responses import JSONResponse
    from fastapi.encoders import jsonable_encoder
    logger.info("FastAPI imported successfully")
except ImportError as e:
    logger.error(f"Failed to import FastAPI: {e}")
    sys.exit(1)

# Create a minimal FastAPI app
logger.info("Creating FastAPI application...")
app = FastAPI(
    title="PI Lawyer AI API",
    description="Minimal runtime for PI Lawyer AI",
    version="1.0.0"
)

# Add basic CORS middleware
logger.info("Adding CORS middleware...")
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, this should be restricted
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

logger.info("FastAPI application created successfully")

# Lazy graph registry
from .graph_registry import GraphRegistry
from .copilotkit_route import router as copilotkit_router

registry = GraphRegistry()

# Mount CopilotKit route (Cloud Remote Endpoint calls this path)
app.include_router(copilotkit_router)

# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint for monitoring and container health checks."""
    import platform
    import time
    from datetime import datetime, timezone

    # Get application start time (or use current time if not available)
    start_time = getattr(app, "start_time", time.time())
    uptime_seconds = int(time.time() - start_time)

    # Basic system information
    system_info = {
        "python_version": platform.python_version(),
        "system": platform.system(),
        "platform": platform.platform(),
    }

    # Include graph readiness info (lazy build attempt for master only if not built)
    master_status = registry.status().get("master", {})
    health = {
        "status": "ok",
        "version": "1.0.0",
        "environment": os.environ.get("APP_ENV", "development"),
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "uptime_seconds": uptime_seconds,
        "system_info": system_info,
        "graphs": {
            "master": master_status,
            "agents": registry.status().get("agents", {}),
        },
    }

    # Try to initialize master graph, but don't fail health on errors
    if not master_status.get("ready"):
        registry.get_master_graph()
        health["graphs"]["master"] = registry.status().get("master", {})

    return health

# Root endpoint
@app.get("/")
async def root():
    """Root endpoint that returns a simple message."""
    return {"message": "PI Lawyer AI API is running"}


# Master graph invocation
@app.post("/agents/master/invoke")
async def invoke_master(payload: Dict[str, Any]):
    """
    Invoke the master routing graph.

    Request JSON:
    {
      "state": { ... },      # required, dict state compatible with master graph
      "config": { ... }      # optional, Runnable config
    }
    """
    master = registry.get_master_graph()
    if master is None:
        status = registry.status().get("master", {})
        raise HTTPException(status_code=503, detail={
            "message": "Master graph not available",
            "status": status,
        })

    state = payload.get("state") or {}
    config = payload.get("config") or {}
    try:
        result = await master.ainvoke(state, config)
        return _encode_state(result)
    except Exception as e:
        logger.exception("Master graph invocation failed: %s", e)
        raise HTTPException(status_code=500, detail=str(e))


# Agent/graph invocation by name
@app.post("/agents/{agent_name}/invoke")
async def invoke_agent(agent_name: str = Path(..., description="Agent or graph name"), payload: Dict[str, Any] = {}):
    """
    Invoke a specific agent or compiled graph by name.

    Supported names (initial):
      - calendar_graph, task_graph
      - matter_client_agent, research_agent, intake_agent
      - document_agent, deadline_agent, supervisor_agent

    Request JSON:
    {
      "state": { ... },
      "config": { ... }
    }
    """
    invoker = registry.get_agent_invoker(agent_name)
    if invoker is None:
        status = registry.status().get("agents", {}).get(agent_name, {})
        raise HTTPException(status_code=503, detail={
            "message": f"Agent '{agent_name}' not available",
            "status": status or {"ready": False, "error": "unknown_or_unavailable"},
        })

    state = payload.get("state") or {}
    config = payload.get("config") or {}
    try:
        result = await invoker.invoke(state, config)
        return _encode_state(result)
    except Exception as e:
        logger.exception("Invocation failed for %s: %s", agent_name, e)
        raise HTTPException(status_code=500, detail=str(e))

def main():
    """Run the uvicorn server."""
    import uvicorn
    port = int(os.getenv("PORT", "8000"))
    uvicorn.run(
        "pi_lawyer.api.runtime:app",
        host="0.0.0.0",
        port=port,
    )

if __name__ == "__main__":
    main()


# ----------------------------
# Response encoding utilities
# ----------------------------

def _encode_state(state: Any) -> Any:
    """Normalize and JSON-encode a LangGraph state or agent output.

    - Flattens LangChain message objects into dicts {role,type,content}
    - Uses FastAPI's jsonable_encoder to ensure JSON serializability
    """
    if isinstance(state, dict):
        state = _normalize_messages_in_state(state)
    return jsonable_encoder(state)


def _normalize_messages_in_state(state: Dict[str, Any]) -> Dict[str, Any]:
    msgs = state.get("messages")
    if not isinstance(msgs, list):
        return state

    normalized: List[Dict[str, Any]] = []
    for m in msgs:
        if isinstance(m, dict):
            role = m.get("role") or m.get("type")
            content = m.get("content")
            # Keep any additional metadata if present
            nm = {k: v for k, v in m.items()}
            if role is not None:
                nm["role"] = role
            if content is not None:
                nm["content"] = content
            normalized.append(nm)
        else:
            # Duck-typing for LangChain messages
            role = getattr(m, "role", None) or getattr(m, "type", None)
            content = getattr(m, "content", None)
            nm: Dict[str, Any] = {}
            if role is not None:
                nm["role"] = role
            if content is not None:
                nm["content"] = content
            normalized.append(nm)

    return {**state, "messages": normalized}
