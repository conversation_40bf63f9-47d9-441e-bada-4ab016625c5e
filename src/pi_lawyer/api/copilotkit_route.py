"""
CopilotKit integration route for FastAPI.

This endpoint adapts CopilotKit Cloud requests to our LangGraph runtime:
- GraphQL "generateCopilotResponse" requests are supported
- Direct JSON requests are supported
- Requests are routed either to a specific agent or to the master router

Notes:
- We normalize inputs to a LangGraph state with simple `{type, content}` messages
- We normalize outputs to CopilotKit's `{ messages, done, threadId }` shape
"""

import logging
from typing import Any, Dict, List, Optional

from fastapi import APIRouter, HTTPException, Request
from fastapi.responses import StreamingResponse

from .graph_registry import GraphRegistry

# Set up logging
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(
    prefix="/copilotkit",
    tags=["copilotkit"],
    responses={404: {"description": "Not found"}},
)

registry = GraphRegistry()


@router.post("")
async def handle_copilotkit_request(request: Request):
    """
    Main endpoint for CopilotKit requests.
    Routes requests to the appropriate agent handler based on the agent name.
    """
    try:
        # Parse request body
        body = await request.json()
        logger.info(f"Received CopilotKit request: {str(body)[:200]}...")

        # Extract GraphQL operation if present
        operation_name = body.get("operationName")

        # Handle different request structures
        if operation_name == "generateCopilotResponse":
            # Extract GraphQL variables
            variables = body.get("variables", {})
            data = variables.get("data", {})

            agent_name = (data.get("agent") or "master").strip()
            thread_id = data.get("threadId") or _ensure_thread_id(data)
            tenant_id = data.get("tenantId") or data.get("tenant_id") or "dev-tenant"
            user_id = data.get("userId") or data.get("user_id") or "anon-user"
            # Build LG state from CopilotKit payload
            state = _build_state_from_data(data)

            # Route to agent or master
            result = await _invoke_agent_or_master(
                agent_name=agent_name,
                state=state,
                tenant_id=tenant_id,
                user_id=user_id,
                thread_id=thread_id,
            )

            # Streaming mode (optional)
            stream_flag = bool(data.get("stream"))
            if stream_flag:
                return _stream_graphql_response(result, thread_id)

            response = _to_copilotkit_response(result, thread_id)
            return format_graphql_response(response)

        else:
            # Direct API request (non-GraphQL)
            agent_name = (body.get("agent") or "master").strip()
            thread_id = body.get("threadId") or _ensure_thread_id(body)
            tenant_id = body.get("tenantId") or body.get("tenant_id") or "dev-tenant"
            user_id = body.get("userId") or body.get("user_id") or "anon-user"
            state = _build_state_from_data(body)

            result = await _invoke_agent_or_master(
                agent_name=agent_name,
                state=state,
                tenant_id=tenant_id,
                user_id=user_id,
                thread_id=thread_id,
            )
            if bool(body.get("stream")):
                return _stream_graphql_response(result, thread_id)
            return _to_copilotkit_response(result, thread_id)

    except Exception as e:
        logger.exception(f"Error handling CopilotKit request: {str(e)}")

        # Return error in GraphQL format if it was a GraphQL request
        if body.get("operationName") == "generateCopilotResponse":
            return format_graphql_response(
                {
                    "messages": [
                        {
                            "content": [
                                "I encountered an error processing your request. Please try again."
                            ],
                            "role": "assistant",
                        }
                    ],
                    "done": True,
                    "threadId": body.get("variables", {})
                    .get("data", {})
                    .get("threadId", "error-thread"),
                }
            )
        else:
            # Regular error for direct API
            raise HTTPException(status_code=500, detail=str(e))


def _ensure_thread_id(data: Dict[str, Any]) -> str:
    """Return threadId from payload or generate a deterministic placeholder."""
    # If not provided, create a minimal stable placeholder for dev
    return data.get("threadId") or "thread-dev"


def _build_state_from_data(data: Dict[str, Any]) -> Dict[str, Any]:
    """Translate CopilotKit payload into a minimal LangGraph state shape."""
    # Extract the most recent user input
    user_text: Optional[str] = None

    # Some payloads provide a single input field
    for key in ("input", "message", "prompt"):
        if isinstance(data.get(key), str) and data[key].strip():
            user_text = data[key].strip()
            break

    # If a messages array exists, try to find the last user message
    if not user_text and isinstance(data.get("messages"), list):
        for m in reversed(data["messages"]):
            # Common roles: user, assistant
            role = (m.get("role") or m.get("type") or "").lower()
            content = m.get("content")
            if role in ("user", "human") and isinstance(content, str) and content.strip():
                user_text = content.strip()
                break

    if not user_text:
        user_text = "Hello"

    # Minimal state the master router understands: list of dict messages {type, content}
    return {
        "messages": [{"type": "human", "content": user_text}],
    }


async def _invoke_agent_or_master(
    *,
    agent_name: str,
    state: Dict[str, Any],
    tenant_id: str,
    user_id: str,
    thread_id: str,
) -> Dict[str, Any]:
    """Invoke a specific agent or master router depending on the name.

    For concrete agents invoking BaseAgent, we must pass config.configurable with
    tenant_id, user_id, and thread_id. For the master graph, config is optional.
    """
    # Route to master when explicitly set or when agent is unknown to registry
    if agent_name.lower() in ("master", "master_router", "router"):
        master = registry.get_master_graph()
        if master is None:
            status = registry.status().get("master", {})
            raise HTTPException(status_code=503, detail={
                "message": "Master graph not available",
                "status": status,
            })
        return await master.ainvoke(state, {})

    # Try concrete agent/graph
    invoker = registry.get_agent_invoker(agent_name)
    if invoker is None:
        # Fallback to master routing
        master = registry.get_master_graph()
        if master is None:
            status = registry.status().get("master", {})
            raise HTTPException(status_code=503, detail={
                "message": "Master graph not available",
                "status": status,
            })
        return await master.ainvoke(state, {})

    # For BaseAgent-backed invokers, pass config with required fields
    config = {"configurable": {"tenant_id": tenant_id, "user_id": user_id, "thread_id": thread_id}}
    return await invoker.invoke(state, config)


def _to_copilotkit_response(result_state: Dict[str, Any], thread_id: str) -> Dict[str, Any]:
    """Normalize LangGraph state into CopilotKit single-message response shape."""
    # Try to extract the last assistant message text
    text = ""
    msgs: List[Any] = result_state.get("messages", []) if isinstance(result_state, dict) else []
    for m in reversed(msgs):
        # Common shapes: dict with role/type/content, or LC message objects
        content = None
        role = None
        if isinstance(m, dict):
            role = (m.get("role") or m.get("type") or "").lower()
            content = m.get("content")
        else:
            # Try duck-typing for langchain messages
            role = getattr(m, "type", None) or getattr(m, "role", None)
            role = role.lower() if isinstance(role, str) else None
            content = getattr(m, "content", None)

        if role in ("assistant", "ai") and isinstance(content, str) and content.strip():
            text = content.strip()
            break

    if not text:
        text = "I processed your request."

    return {
        "messages": [{"content": [text], "role": "assistant"}],
        "done": True,
        "threadId": thread_id,
    }


def _stream_graphql_response(result_state: Dict[str, Any], thread_id: str) -> StreamingResponse:
    """Return a simple SSE stream of the assistant message content.

    This implements a minimal streaming mode compatible with CopilotKit Cloud.
    """

    def _iter():  # pragma: no cover - streaming generator
        import json
        text = ""
        msgs = result_state.get("messages", []) if isinstance(result_state, dict) else []
        for m in msgs[::-1]:
            role = (m.get("role") or m.get("type") or "").lower() if isinstance(m, dict) else None
            content = m.get("content") if isinstance(m, dict) else getattr(m, "content", None)
            if role in ("assistant", "ai") and isinstance(content, str):
                text = content
                break
        if not text:
            text = "I processed your request."

        # Chunk the text into small pieces
        chunk_size = 200
        for i in range(0, len(text), chunk_size):
            chunk = text[i : i + chunk_size]
            event = {
                "messages": [{"content": [chunk], "role": "assistant"}],
                "done": False,
                "threadId": thread_id,
            }
            yield f"data: {json.dumps(event)}\n\n"

        # Final done event
        final = {"messages": [], "done": True, "threadId": thread_id}
        yield f"data: {json.dumps(final)}\n\n"

    return StreamingResponse(_iter(), media_type="text/event-stream")


def format_graphql_response(result: Dict[str, Any]) -> Dict[str, Any]:
    """Format a result dictionary as a GraphQL response."""
    return {"data": {"generateCopilotResponse": result}}


@router.options("")
async def options_handler():
    """Handle CORS preflight requests."""
    return {}


@router.get("/available-agents")
async def get_available_agents():
    """Return a list of available agents."""
    return {
        "data": {
            "availableAgents": [
                {
                    "id": "research_agent",
                    "name": "Legal Research Assistant",
                    "description": "Specialized agent for legal research in personal injury cases",
                    "capabilities": [
                        "Texas law research",
                        "Personal injury expertise",
                        "Case law citation",
                    ],
                }
                # Add more agents as they become available
            ]
        }
    }
