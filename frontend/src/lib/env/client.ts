/**
 * Client-side environment variable accessor
 *
 * This module provides safe access to environment variables in client-side code.
 * Only NEXT_PUBLIC_* variables are accessible on the client side.
 *
 * This avoids direct process.env usage in client code which triggers ESLint no-undef.
 */

interface ClientEnv {
  NEXT_PUBLIC_SUPABASE_URL: string;
  NEXT_PUBLIC_SUPABASE_ANON_KEY: string;
  NEXT_PUBLIC_AGUI_ENABLED?: string;
  NEXT_PUBLIC_USE_SUPABASE_AUTH?: string;
  NEXT_PUBLIC_API_BASE_URL?: string;
  NEXT_PUBLIC_BACKEND_API_URL?: string;
  NEXT_PUBLIC_LAWS_API_BASE?: string;
  NEXT_PUBLIC_MCP_RULES_BASE_URL?: string;
  NEXT_PUBLIC_MCP_API_TIMEOUT?: string;
  NEXT_PUBLIC_MCP_MAX_RETRIES?: string;
  NEXT_PUBLIC_MCP_RETRY_DELAY?: string;
  NEXT_PUBLIC_ENABLE_INSIGHTS?: string;
  NEXT_PUBLIC_ENABLE_CHAT?: string;
  NEXT_PUBLIC_FEATURE_MCP_RULES_ENGINE?: string;
  NEXT_PUBLIC_D2_STAGING_MODE?: string;
  NEXT_PUBLIC_D3_PILOT_TENANT?: string;
  NEXT_PUBLIC_MCP_DEBUG_LOGGING?: string;
  NEXT_PUBLIC_MCP_METRICS_ENABLED?: string;
  NEXT_PUBLIC_VERCEL_ENV?: string;
  NEXT_PUBLIC_SENTRY_DSN?: string;
  NEXT_PUBLIC_APP_VERSION?: string;
  NEXT_PUBLIC_FPJS_API_KEY?: string;
  NEXT_PUBLIC_FPJS_PUBLIC_KEY?: string;
  NEXT_PUBLIC_COPILOT_KEY?: string;
  NEXT_PUBLIC_QUEUE_POLLING_INTERVAL?: string;
  NEXT_PUBLIC_DISABLE_TEST_ENDPOINTS?: string;
  NEXT_PUBLIC_SUPER_ADMIN_EMAILS?: string;
  NODE_ENV: string;
}

/**
 * Get client-side environment variables safely
 * Only returns NEXT_PUBLIC_* variables that are available on the client
 */
// Create a static environment object that Next.js will replace at build time
const clientEnvVars = {
  NEXT_PUBLIC_SUPABASE_URL: process.env.NEXT_PUBLIC_SUPABASE_URL || "",
  NEXT_PUBLIC_SUPABASE_ANON_KEY: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || "",
  NEXT_PUBLIC_AGUI_ENABLED: process.env.NEXT_PUBLIC_AGUI_ENABLED,
  NEXT_PUBLIC_USE_SUPABASE_AUTH: process.env.NEXT_PUBLIC_USE_SUPABASE_AUTH,
  NEXT_PUBLIC_API_BASE_URL: process.env.NEXT_PUBLIC_API_BASE_URL,
  NEXT_PUBLIC_BACKEND_API_URL: process.env.NEXT_PUBLIC_BACKEND_API_URL,
  NEXT_PUBLIC_LAWS_API_BASE: process.env.NEXT_PUBLIC_LAWS_API_BASE,
  NEXT_PUBLIC_MCP_RULES_BASE_URL: process.env.NEXT_PUBLIC_MCP_RULES_BASE_URL,
  NEXT_PUBLIC_MCP_API_TIMEOUT: process.env.NEXT_PUBLIC_MCP_API_TIMEOUT,
  NEXT_PUBLIC_MCP_MAX_RETRIES: process.env.NEXT_PUBLIC_MCP_MAX_RETRIES,
  NEXT_PUBLIC_MCP_RETRY_DELAY: process.env.NEXT_PUBLIC_MCP_RETRY_DELAY,
  NEXT_PUBLIC_ENABLE_INSIGHTS: process.env.NEXT_PUBLIC_ENABLE_INSIGHTS,
  NEXT_PUBLIC_ENABLE_CHAT: process.env.NEXT_PUBLIC_ENABLE_CHAT,
  NEXT_PUBLIC_FEATURE_MCP_RULES_ENGINE: process.env.NEXT_PUBLIC_FEATURE_MCP_RULES_ENGINE,
  NEXT_PUBLIC_D2_STAGING_MODE: process.env.NEXT_PUBLIC_D2_STAGING_MODE,
  NEXT_PUBLIC_D3_PILOT_TENANT: process.env.NEXT_PUBLIC_D3_PILOT_TENANT,
  NEXT_PUBLIC_MCP_DEBUG_LOGGING: process.env.NEXT_PUBLIC_MCP_DEBUG_LOGGING,
  NEXT_PUBLIC_MCP_METRICS_ENABLED: process.env.NEXT_PUBLIC_MCP_METRICS_ENABLED,
  NEXT_PUBLIC_VERCEL_ENV: process.env.NEXT_PUBLIC_VERCEL_ENV,
  NEXT_PUBLIC_SENTRY_DSN: process.env.NEXT_PUBLIC_SENTRY_DSN,
  NEXT_PUBLIC_APP_VERSION: process.env.NEXT_PUBLIC_APP_VERSION,
  NEXT_PUBLIC_FPJS_API_KEY: process.env.NEXT_PUBLIC_FPJS_API_KEY,
  NEXT_PUBLIC_FPJS_PUBLIC_KEY: process.env.NEXT_PUBLIC_FPJS_PUBLIC_KEY,
  NEXT_PUBLIC_COPILOT_KEY: process.env.NEXT_PUBLIC_COPILOT_KEY,
  NEXT_PUBLIC_QUEUE_POLLING_INTERVAL: process.env.NEXT_PUBLIC_QUEUE_POLLING_INTERVAL,
  NEXT_PUBLIC_DISABLE_TEST_ENDPOINTS: process.env.NEXT_PUBLIC_DISABLE_TEST_ENDPOINTS,
  NEXT_PUBLIC_SUPER_ADMIN_EMAILS: process.env.NEXT_PUBLIC_SUPER_ADMIN_EMAILS,
  NODE_ENV: process.env.NODE_ENV || "development",
} as const;

function getClientEnv(): ClientEnv {
  return clientEnvVars;
}

/**
 * Client environment variables
 * Use this instead of direct process.env access in client-side code
 */
export const clientEnv = getClientEnv();

/**
 * Legacy export for compatibility with existing code
 */
export const env = clientEnv;

/**
 * Check if we're in development mode
 */
export const isDevelopment = clientEnv.NODE_ENV === "development";

/**
 * Check if we're in production mode
 */
export const isProduction = clientEnv.NODE_ENV === "production";

/**
 * Get Supabase configuration for client
 */
export function getSupabaseConfig() {
  return {
    url: clientEnv.NEXT_PUBLIC_SUPABASE_URL,
    anonKey: clientEnv.NEXT_PUBLIC_SUPABASE_ANON_KEY,
  };
}

/**
 * Get FingerprintJS configuration for client
 */
export function getFingerprintJSConfig() {
  return {
    publicKey: clientEnv.NEXT_PUBLIC_FPJS_PUBLIC_KEY,
  };
}

/**
 * Get CopilotKit configuration for client
 */
export function getCopilotKitConfig() {
  return {
    apiKey: clientEnv.NEXT_PUBLIC_COPILOT_KEY,
  };
}
