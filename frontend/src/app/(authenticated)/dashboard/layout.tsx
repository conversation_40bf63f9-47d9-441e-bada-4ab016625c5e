"use client";

import { Co<PERSON>lotKit } from "@copilotkit/react-core";
import type { ReactNode, ReactElement } from "react";
import { UserActivityProvider } from "@/components/providers/UserActivityProvider";
import { getCopilotKitConfig } from "@/lib/env/client";

export default function DashboardLayout({
  children,
}: {
  children: ReactNode;
}): ReactElement {
  const copilotConfig = getCopilotKitConfig();

  return (
    <UserActivityProvider>
      <CopilotKit
        publicApiKey={copilotConfig.apiKey}
        agent="supervisor_agent"
        properties={{
          userRole: "staff",
          // Deterministic thread ID for tenant isolation
          threadId: crypto.randomUUID(), // In production, use org+user ID hash
        }}
      >
        {children}
      </CopilotKit>
    </UserActivityProvider>
  );
}
