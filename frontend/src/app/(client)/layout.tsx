// frontend/src/app/(client)/layout.tsx
"use client";

import { useEffect, useState, type ReactNode, type ReactElement } from "react";
import { useRouter } from "next/navigation";
import { useSession } from "@/contexts/SessionContext";
import { CopilotKit } from "@copilotkit/react-core";
import { getCopilotKitConfig } from "@/lib/env/client";

export default function ClientLayout({
  children,
}: {
  children: ReactNode;
}): ReactElement {
  const router = useRouter();
  const { session } = useSession();
  const [isLoading, setIsLoading] = useState(true);
  const copilotConfig = getCopilotKitConfig();

  useEffect(() => {
    if (!session) {
      router.push("/login");
    } else {
      setIsLoading(false);
    }
  }, [session, router]);

  if (isLoading) {
    return <div>Loading...</div>;
  }

  return (
    <CopilotKit
      publicApiKey={copilotConfig.apiKey}
      // Remove agent specification to use general Copilot Cloud LLM
      properties={{
        userRole: "client",
        // Generate deterministic thread ID for tenant isolation
        threadId: session?.user?.id ? `client-${session.user.id}` : undefined,
      }}
    >
      <div className="min-h-screen bg-gray-50">{children}</div>
    </CopilotKit>
  );
}
