import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";

// Mock implementation for testing
export async function GET(request: NextRequest) {
  // Add cache headers to reduce API calls
  const response = NextResponse.next();
  response.headers.set('Cache-Control', 'public, max-age=300, s-maxage=300'); // 5 minutes cache
  try {
    const { searchParams } = new URL(request.url);
    const placement = searchParams.get("placement") || "footer";
    const practiceArea = searchParams.get("practice_area");
    const jurisdiction = searchParams.get("jurisdiction");
    const language = searchParams.get("language") || "en";

    // Mock user region detection (in real implementation, this would use data residency middleware)
    const userRegion = "US"; // Default to US for testing

    // Mock disclaimers data
    const mockDisclaimers = [
      {
        id: "1",
        title: "AI Assistant Disclaimer",
        content:
          "AiLex AI Assistant: This AI-powered tool is designed to assist legal professionals and is not a substitute for professional legal judgment. Users remain responsible for ensuring compliance with applicable professional conduct rules, client confidentiality requirements, and jurisdictional regulations. The AI system should be used as a research and drafting aid, with all output subject to attorney review and verification.",
        disclaimer_type: "no_legal_advice",
        region: "US",
        placement: ["modal", "footer"],
        priority: 1,
        is_required: true,
        effective_date: new Date().toISOString(),
        metadata: {
          compliance_frameworks: ["ABA Model Rules"],
          practice_areas: ["all"],
        },
      },
      {
        id: "2",
        title: "Professional Responsibility",
        content:
          "Professional Responsibility Notice: Users of this AI system must ensure compliance with all applicable rules of professional conduct, including but not limited to client confidentiality, competence requirements, and supervision obligations. This tool does not replace the attorney's duty to exercise independent professional judgment.",
        disclaimer_type: "professional_responsibility",
        region: "US",
        placement: ["footer"],
        priority: 2,
        is_required: true,
        effective_date: new Date().toISOString(),
        metadata: {
          compliance_frameworks: ["State Bar Rules"],
          jurisdictions: ["texas", "california", "new_york"],
        },
      },
    ];

    // Filter disclaimers based on placement and region
    const filteredDisclaimers = mockDisclaimers.filter(
      (disclaimer) =>
        disclaimer.region === userRegion &&
        disclaimer.placement.includes(placement),
    );

    const response = {
      disclaimers: filteredDisclaimers,
      total_count: filteredDisclaimers.length,
      context: {
        user_region: userRegion,
        placement: placement,
        practice_area: practiceArea,
        jurisdiction: jurisdiction,
      },
      cache_key: `disclaimers:${userRegion}:${placement}:${practiceArea || "all"}`,
      last_updated: new Date().toISOString(),
    };

    const jsonResponse = NextResponse.json(response);
    // Add cache headers to the JSON response
    jsonResponse.headers.set('Cache-Control', 'public, max-age=300, s-maxage=300');
    return jsonResponse;
  } catch (error) {
    console.error("Error in regional disclaimers API:", error);
    return NextResponse.json(
      { error: "Failed to fetch disclaimers" },
      { status: 500 },
    );
  }
}
