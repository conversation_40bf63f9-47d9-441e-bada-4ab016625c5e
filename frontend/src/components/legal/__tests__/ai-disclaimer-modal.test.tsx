import React from "react";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { vi, describe, it, expect, beforeEach } from "vitest";
import { AIDisclaimerModal } from "../ai-disclaimer-modal";

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};
Object.defineProperty(window, "localStorage", {
  value: localStorageMock,
});

// Mock fetch for API calls
global.fetch = vi.fn();

describe("AIDisclaimerModal", () => {
  beforeEach(() => {
    vi.clearAllMocks();
    localStorageMock.getItem.mockReturnValue(null);
    
    // Mock successful API response
    (global.fetch as jest.Mock).mockResolvedValue({
      ok: true,
      json: async () => ({
        disclaimers: [
          {
            id: "disclaimer-1",
            title: "AI Assistant Disclaimer",
            content: "AiLex AI Assistant: This AI-powered tool is designed to assist legal professionals and is not a substitute for professional legal judgment. Users remain responsible for ensuring compliance with applicable professional conduct rules, client confidentiality requirements, and jurisdictional regulations. The AI system should be used as a research and drafting aid, with all output subject to attorney review and verification.",
            disclaimer_type: "ai_assistance",
            region: "US",
            placement: ["modal"],
            priority: 1,
            is_required: true,
          },
        ],
        total_count: 1,
        context: {
          user_region: "US",
          placement: "modal",
        },
      }),
    });
  });

  it("should enable 'I Acknowledge' button when required disclaimer is checked", async () => {
    const user = userEvent.setup();
    
    render(
      <AIDisclaimerModal
        autoShow={true}
        onAcknowledge={vi.fn()}
        onDismiss={vi.fn()}
      />
    );

    // Wait for modal to appear and disclaimers to load
    await waitFor(() => {
      expect(screen.getByText("AiLex AI Assistant - Important Legal Notice")).toBeInTheDocument();
    });

    // Initially, the "I Acknowledge" button should be disabled
    const acknowledgeButton = screen.getByRole("button", { name: /i acknowledge/i });
    expect(acknowledgeButton).toBeDisabled();

    // Find and check the disclaimer checkbox
    const checkbox = screen.getByRole("checkbox");
    expect(checkbox).not.toBeChecked();

    // Click the checkbox to acknowledge the disclaimer
    await user.click(checkbox);

    // Verify checkbox is now checked
    expect(checkbox).toBeChecked();

    // The "I Acknowledge" button should now be enabled
    await waitFor(() => {
      expect(acknowledgeButton).not.toBeDisabled();
    });
  });

  it("should disable 'I Acknowledge' button when required disclaimer is unchecked", async () => {
    const user = userEvent.setup();
    
    render(
      <AIDisclaimerModal
        autoShow={true}
        onAcknowledge={vi.fn()}
        onDismiss={vi.fn()}
      />
    );

    // Wait for modal to appear
    await waitFor(() => {
      expect(screen.getByText("AiLex AI Assistant - Important Legal Notice")).toBeInTheDocument();
    });

    const acknowledgeButton = screen.getByRole("button", { name: /i acknowledge/i });
    const checkbox = screen.getByRole("checkbox");

    // Check the disclaimer first
    await user.click(checkbox);
    expect(checkbox).toBeChecked();
    
    await waitFor(() => {
      expect(acknowledgeButton).not.toBeDisabled();
    });

    // Uncheck the disclaimer
    await user.click(checkbox);
    expect(checkbox).not.toBeChecked();

    // Button should be disabled again
    await waitFor(() => {
      expect(acknowledgeButton).toBeDisabled();
    });
  });

  it("should close modal when 'I Acknowledge' button is clicked", async () => {
    const user = userEvent.setup();
    const onAcknowledge = vi.fn();
    
    render(
      <AIDisclaimerModal
        autoShow={true}
        onAcknowledge={onAcknowledge}
        onDismiss={vi.fn()}
      />
    );

    // Wait for modal to appear
    await waitFor(() => {
      expect(screen.getByText("AiLex AI Assistant - Important Legal Notice")).toBeInTheDocument();
    });

    const acknowledgeButton = screen.getByRole("button", { name: /i acknowledge/i });
    const checkbox = screen.getByRole("checkbox");

    // Check the disclaimer to enable the button
    await user.click(checkbox);
    
    await waitFor(() => {
      expect(acknowledgeButton).not.toBeDisabled();
    });

    // Click the acknowledge button
    await user.click(acknowledgeButton);

    // Verify the onAcknowledge callback was called
    await waitFor(() => {
      expect(onAcknowledge).toHaveBeenCalled();
    });

    // Verify localStorage was updated
    expect(localStorageMock.setItem).toHaveBeenCalledWith(
      "ailex_disclaimer_acknowledged",
      expect.stringContaining('"disclaimers":["disclaimer-1"]')
    );
  });

  it("should handle 'Remind Me Later' button correctly", async () => {
    const user = userEvent.setup();
    const onDismiss = vi.fn();
    
    render(
      <AIDisclaimerModal
        autoShow={true}
        onAcknowledge={vi.fn()}
        onDismiss={onDismiss}
      />
    );

    // Wait for modal to appear
    await waitFor(() => {
      expect(screen.getByText("AiLex AI Assistant - Important Legal Notice")).toBeInTheDocument();
    });

    const remindLaterButton = screen.getByRole("button", { name: /remind me later/i });

    // Click the remind later button
    await user.click(remindLaterButton);

    // Verify the onDismiss callback was called
    await waitFor(() => {
      expect(onDismiss).toHaveBeenCalled();
    });

    // Verify localStorage was updated with dismissal
    expect(localStorageMock.setItem).toHaveBeenCalledWith(
      "ailex_disclaimer_dismissed",
      expect.stringContaining('"date":')
    );
  });
});
