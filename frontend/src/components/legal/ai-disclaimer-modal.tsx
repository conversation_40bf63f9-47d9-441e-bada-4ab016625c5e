"use client";

import React, { useState, useEffect, useCallback } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Brain, Scale, Shield, AlertTriangle, CheckCircle } from "lucide-react";

// Types
interface LegalDisclaimer {
  id: string;
  title: string;
  content: string;
  disclaimer_type: string;
  region: string;
  placement: string[];
  priority: number;
  is_required: boolean;
}

interface DisclaimerResponse {
  disclaimers: LegalDisclaimer[];
  total_count: number;
  context: {
    user_region: string;
    placement: string;
  };
}

interface AIDisclaimerModalProps {
  /** Whether to show the modal automatically on first visit */
  autoShow?: boolean;
  /** How many days to wait before showing again after dismissal */
  dismissalDays?: number;
  /** Practice area context for filtering disclaimers */
  practiceArea?: string;
  /** Jurisdiction context */
  jurisdiction?: string;
  /** Custom trigger button (if not auto-showing) */
  trigger?: React.ReactNode;
  /** Callback when modal is acknowledged */
  onAcknowledge?: () => void;
  /** Callback when modal is dismissed */
  onDismiss?: () => void;
}

const STORAGE_KEY = "ailex_disclaimer_acknowledged";
const STORAGE_DISMISSAL_KEY = "ailex_disclaimer_dismissed";

export function AIDisclaimerModal({
  autoShow = true,
  dismissalDays = 30,
  practiceArea,
  jurisdiction,
  trigger,
  onAcknowledge,
  onDismiss,
}: AIDisclaimerModalProps): React.ReactElement {
  const [isOpen, setIsOpen] = useState(false);
  const [disclaimers, setDisclaimers] = useState<LegalDisclaimer[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [userRegion, setUserRegion] = useState<string>("US");
  const [hasAcknowledged, setHasAcknowledged] = useState(false);
  const [acknowledgedDisclaimers, setAcknowledgedDisclaimers] = useState<
    Set<string>
  >(new Set());

  // Check if user has already acknowledged or dismissed
  const checkAcknowledgmentStatus = (): boolean => {
    try {
      const acknowledged = localStorage.getItem(STORAGE_KEY);
      const dismissed = localStorage.getItem(STORAGE_DISMISSAL_KEY);

      if (acknowledged) {
        const acknowledgedData = JSON.parse(acknowledged) as { date: string };
        const acknowledgedDate = new Date(acknowledgedData.date);
        const daysSinceAcknowledged =
          (Date.now() - acknowledgedDate.getTime()) / (1000 * 60 * 60 * 24);

        // If acknowledged within the last 90 days, don't show
        if (daysSinceAcknowledged < 90) {
          return true;
        }
      }

      if (dismissed) {
        const dismissedData = JSON.parse(dismissed) as { date: string };
        const dismissedDate = new Date(dismissedData.date);
        const daysSinceDismissed =
          (Date.now() - dismissedDate.getTime()) / (1000 * 60 * 60 * 24);

        // If dismissed within the dismissal period, don't show
        if (daysSinceDismissed < dismissalDays) {
          return true;
        }
      }

      return false;
    } catch (error) {
      console.warn("Error checking disclaimer acknowledgment status:", error);
      return false;
    }
  };

  // Fetch AI disclaimers with caching to prevent rate limiting
  const fetchDisclaimers = useCallback(async (): Promise<void> => {
    try {
      setLoading(true);
      setError(null);

      // Check cache first to prevent repeated API calls
      const cacheKey = `disclaimers_${practiceArea || "ai_assistance"}_${jurisdiction || "default"}`;
      const cached = sessionStorage.getItem(cacheKey);

      if (cached) {
        try {
          const cachedData = JSON.parse(cached) as {
            disclaimers: LegalDisclaimer[];
            context: { user_region: string };
            timestamp: number;
          };

          // Use cached data if less than 5 minutes old
          if (Date.now() - cachedData.timestamp < 5 * 60 * 1000) {
            setDisclaimers(cachedData.disclaimers);
            setUserRegion(cachedData.context.user_region);
            setLoading(false);
            return;
          }
        } catch (cacheError) {
          console.warn("Failed to parse cached disclaimers:", cacheError);
        }
      }

      const params = new URLSearchParams({
        placement: "modal",
        language: "en",
      });

      if (practiceArea) params.append("practice_area", practiceArea);
      if (jurisdiction) params.append("jurisdiction", jurisdiction);

      const response = await fetch(`/api/regional-disclaimers?${params}`, {
        headers: {
          'Cache-Control': 'max-age=300', // 5 minutes cache
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch disclaimers: ${response.statusText}`);
      }

      const data: DisclaimerResponse = await response.json();

      // Filter for AI-related disclaimers
      const aiDisclaimers = data.disclaimers.filter(
        (d) =>
          d.disclaimer_type === "no_legal_advice" ||
          d.disclaimer_type === "professional_responsibility" ||
          d.disclaimer_type === "confidentiality_warning",
      );

      setDisclaimers(aiDisclaimers);
      setUserRegion(data.context.user_region);

      // Cache the response
      try {
        sessionStorage.setItem(cacheKey, JSON.stringify({
          disclaimers: aiDisclaimers,
          context: data.context,
          timestamp: Date.now(),
        }));
      } catch (cacheError) {
        console.warn("Failed to cache disclaimers:", cacheError);
      }
    } catch (err) {
      console.error("Failed to fetch AI disclaimers:", err);
      setError(err instanceof Error ? err.message : "Unknown error");

      // Fallback: Use a basic disclaimer when API fails
      const fallbackDisclaimer: LegalDisclaimer = {
        id: "fallback-ai-disclaimer",
        title: "AI Assistant Disclaimer",
        content: "This AI-powered tool is designed to assist legal professionals and is not a substitute for professional legal judgment. Users remain responsible for ensuring compliance with applicable professional conduct rules, client confidentiality requirements, and jurisdictional regulations.",
        disclaimer_type: "no_legal_advice",
        region: "US",
        placement: ["modal"],
        priority: 1,
        is_required: true,
      };

      setDisclaimers([fallbackDisclaimer]);
      setUserRegion("US");
    } finally {
      setLoading(false);
    }
  }, [practiceArea, jurisdiction]);

  // Initialize modal
  useEffect(() => {
    const shouldShow = autoShow && !checkAcknowledgmentStatus();

    if (shouldShow && !isOpen && !loading) {
      void fetchDisclaimers().then(() => {
        setIsOpen(true);
      });
    }
  }, [autoShow, dismissalDays]); // Removed problematic dependencies

  // Handle individual disclaimer acknowledgment - Fixed checkbox interaction bug
  const handleDisclaimerAcknowledge = (disclaimerId: string, checked: boolean): void => {
    setAcknowledgedDisclaimers((prev) => {
      const newSet = new Set(prev);
      if (checked) {
        newSet.add(disclaimerId);
      } else {
        newSet.delete(disclaimerId);
      }
      return newSet;
    });
  };

  // Handle full acknowledgment
  const handleAcknowledge = async (): Promise<void> => {
    try {
      // Store acknowledgment in localStorage
      const acknowledgmentData = {
        date: new Date().toISOString(),
        disclaimers: disclaimers.map((d) => d.id),
        region: userRegion,
      };

      localStorage.setItem(STORAGE_KEY, JSON.stringify(acknowledgmentData));

      // TODO: Send acknowledgment to backend for compliance tracking
      // await fetch('/api/regional-disclaimers/acknowledge', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify({
      //     disclaimer_ids: disclaimers.map(d => d.id),
      //     acknowledgment_method: 'modal'
      //   })
      // })

      setHasAcknowledged(true);
      setIsOpen(false);
      onAcknowledge?.();
    } catch (error) {
      console.error("Failed to record acknowledgment:", error);
      setError("Failed to record acknowledgment. Please try again.");
    }
  };

  // Handle dismissal
  const handleDismiss = (): void => {
    try {
      const dismissalData = {
        date: new Date().toISOString(),
        region: userRegion,
      };

      localStorage.setItem(
        STORAGE_DISMISSAL_KEY,
        JSON.stringify(dismissalData),
      );

      setIsOpen(false);
      onDismiss?.();
    } catch (error) {
      console.error("Failed to record dismissal:", error);
    }
  };

  // Manual trigger
  const handleManualOpen = (): void => {
    void fetchDisclaimers().then(() => {
      setIsOpen(true);
    });
  };

  // Check if all required disclaimers are acknowledged
  const requiredDisclaimers = disclaimers.filter((d) => d.is_required);
  const allRequiredAcknowledged = requiredDisclaimers.length > 0 && requiredDisclaimers.every((d) =>
    acknowledgedDisclaimers.has(d.id),
  );

  if (trigger) {
    return (
      <>
        <div onClick={handleManualOpen} className="cursor-pointer">
          {trigger}
        </div>

        <Dialog open={isOpen} onOpenChange={setIsOpen}>
          <DialogContent className="max-w-2xl max-h-[80vh]">
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                <Brain className="h-6 w-6 text-blue-600" />
                AiLex AI Assistant - Important Legal Notice
                <Badge variant="outline" className="ml-2">
                  {userRegion === "US" ? "United States" : "European Union"}
                </Badge>
              </DialogTitle>
              <DialogDescription>
                Please review and acknowledge these important notices about
                using AiLex AI tools.
              </DialogDescription>
            </DialogHeader>

            {error && (
              <Alert className="border-red-200 bg-red-50">
                <AlertTriangle className="h-4 w-4 text-red-600" />
                <AlertDescription className="text-red-800">
                  {error}
                </AlertDescription>
              </Alert>
            )}

            <ScrollArea className="max-h-[50vh]">
              <div className="space-y-6">
                {disclaimers.map((disclaimer) => (
                  <div key={disclaimer.id} className="border rounded-lg p-4">
                    <div className="flex items-start gap-3">
                      <div className="flex items-center justify-center w-8 h-8 bg-blue-100 rounded-lg flex-shrink-0 mt-1">
                        <Scale className="h-4 w-4 text-blue-600" />
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <h4 className="font-semibold">{disclaimer.title}</h4>
                          {disclaimer.is_required && (
                            <Badge variant="destructive" className="text-xs">
                              Required
                            </Badge>
                          )}
                        </div>
                        <p className="text-sm text-gray-600 whitespace-pre-wrap leading-relaxed">
                          {disclaimer.content}
                        </p>

                        {disclaimer.is_required && (
                          <div className="flex items-center space-x-2 mt-3">
                            <Checkbox
                              id={`disclaimer-${disclaimer.id}`}
                              checked={acknowledgedDisclaimers.has(
                                disclaimer.id,
                              )}
                              onCheckedChange={(checked) =>
                                handleDisclaimerAcknowledge(disclaimer.id, checked as boolean)
                              }
                            />
                            <label
                              htmlFor={`disclaimer-${disclaimer.id}`}
                              className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                            >
                              I understand and acknowledge this notice
                            </label>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </ScrollArea>

            <DialogFooter className="flex items-center justify-between">
              <div className="flex items-center gap-2 text-sm text-gray-500">
                <Shield className="h-4 w-4" />
                <span>Your acknowledgment helps us maintain compliance</span>
              </div>

              <div className="flex items-center gap-2">
                <Button variant="outline" onClick={handleDismiss}>
                  Remind Me Later
                </Button>
                <Button
                  onClick={() => void handleAcknowledge()}
                  disabled={!allRequiredAcknowledged || loading}
                  className="flex items-center gap-2"
                >
                  <CheckCircle className="h-4 w-4" />I Acknowledge
                </Button>
              </div>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </>
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogContent className="max-w-2xl max-h-[80vh]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Brain className="h-6 w-6 text-blue-600" />
            AiLex AI Assistant - Important Legal Notice
            <Badge variant="outline" className="ml-2">
              {userRegion === "US" ? "United States" : "European Union"}
            </Badge>
          </DialogTitle>
          <DialogDescription>
            Please review and acknowledge these important notices about using
            AiLex AI tools.
          </DialogDescription>
        </DialogHeader>

        {error && (
          <Alert className="border-red-200 bg-red-50">
            <AlertTriangle className="h-4 w-4 text-red-600" />
            <AlertDescription className="text-red-800">
              {error}
            </AlertDescription>
          </Alert>
        )}

        <ScrollArea className="max-h-[50vh]">
          <div className="space-y-6">
            {disclaimers.map((disclaimer) => (
              <div key={disclaimer.id} className="border rounded-lg p-4">
                <div className="flex items-start gap-3">
                  <div className="flex items-center justify-center w-8 h-8 bg-blue-100 rounded-lg flex-shrink-0 mt-1">
                    <Scale className="h-4 w-4 text-blue-600" />
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <h4 className="font-semibold">{disclaimer.title}</h4>
                      {disclaimer.is_required && (
                        <Badge variant="destructive" className="text-xs">
                          Required
                        </Badge>
                      )}
                    </div>
                    <p className="text-sm text-gray-600 whitespace-pre-wrap leading-relaxed">
                      {disclaimer.content}
                    </p>

                    {disclaimer.is_required && (
                      <div className="flex items-center space-x-2 mt-3">
                        <Checkbox
                          id={`disclaimer-${disclaimer.id}`}
                          checked={acknowledgedDisclaimers.has(disclaimer.id)}
                          onCheckedChange={(checked) =>
                            handleDisclaimerAcknowledge(disclaimer.id, checked as boolean)
                          }
                        />
                        <label
                          htmlFor={`disclaimer-${disclaimer.id}`}
                          className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                        >
                          I understand and acknowledge this notice
                        </label>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </ScrollArea>

        <DialogFooter className="flex items-center justify-between">
          <div className="flex items-center gap-2 text-sm text-gray-500">
            <Shield className="h-4 w-4" />
            <span>Your acknowledgment helps us maintain compliance</span>
          </div>

          <div className="flex items-center gap-2">
            <Button variant="outline" onClick={handleDismiss}>
              Remind Me Later
            </Button>
            <Button
              onClick={() => void handleAcknowledge()}
              disabled={!allRequiredAcknowledged || loading}
              className="flex items-center gap-2"
            >
              <CheckCircle className="h-4 w-4" />I Acknowledge
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
